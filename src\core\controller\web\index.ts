// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

import { createServiceRegistry, ServiceMethodHandler, StreamingMethodHandler } from "../grpc-service"
import { StreamingResponseHandler } from "../grpc-handler"
import { registerAllMethods } from "./methods"

// Create web service registry
const webService = createServiceRegistry("web")

// Export the method handler types and registration function
export type WebMethodHandler = ServiceMethodHandler
export type WebStreamingMethodHandler = StreamingMethodHandler
export const registerMethod = webService.registerMethod

// Export the request handlers
export const handleWebServiceRequest = webService.handleRequest
export const handleWebServiceStreamingRequest = webService.handleStreamingRequest
export const isStreamingMethod = webService.isStreamingMethod

// Register all web methods
registerAllMethods()
