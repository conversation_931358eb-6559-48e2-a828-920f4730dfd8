// Simple test script to verify the new terminal architecture
const { TerminalManager } = require('./out/src/integrations/terminal/TerminalManager.js');
const { TerminalRegistry } = require('./out/src/integrations/terminal/TerminalRegistry.js');

console.log('Testing new terminal architecture...');

// Test 1: Create TerminalManager
try {
    const manager = new TerminalManager();
    console.log('✓ TerminalManager created successfully');
    
    // Test 2: Create terminal
    const terminalInfo = TerminalRegistry.createTerminal();
    console.log('✓ Terminal created with ID:', terminalInfo.id);
    console.log('✓ Terminal has buffer:', !!terminalInfo.buffer);
    console.log('✓ Buffer structure:', Object.keys(terminalInfo.buffer));
    
    // Test 3: Check buffer initialization
    if (terminalInfo.buffer.output === "" && 
        terminalInfo.buffer.lastRetrievedIndex === 0 && 
        Array.isArray(terminalInfo.buffer.executionHistory)) {
        console.log('✓ Buffer initialized correctly');
    } else {
        console.log('✗ Buffer initialization failed');
    }
    
    // Test 4: Test getUnretrievedOutput
    const output = manager.getUnretrievedOutput(terminalInfo.id);
    console.log('✓ getUnretrievedOutput works, returned:', JSON.stringify(output));
    
    // Clean up
    manager.disposeAll();
    console.log('✓ Manager disposed successfully');
    
    console.log('\n🎉 All basic tests passed! The new architecture is working.');
    
} catch (error) {
    console.error('✗ Test failed:', error.message);
    console.error(error.stack);
}
