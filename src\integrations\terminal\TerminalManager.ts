import pWaitFor from "p-wait-for"
import * as vscode from "vscode"
import { arePathsEqual } from "@utils/path"
import { mergePromise, TerminalProcess, TerminalProcessResultPromise } from "./TerminalProcess"
import { TerminalInfo, TerminalRegistry } from "./TerminalRegistry"
import { stripAnsi } from "./ansiUtils"

/*
TerminalManager:
- Creates/reuses terminals
- Runs commands via runCommand(), returning a TerminalProcess
- Handles shell integration events

TerminalProcess extends EventEmitter and implements Promise:
- Emits 'line' events with output while promise is pending
- process.continue() resolves promise and stops event emission
- Allows real-time output handling or background execution

getUnretrievedOutput() fetches latest output for ongoing commands

Enables flexible command execution:
- Await for completion
- Listen to real-time events
- Continue execution in background
- Retrieve missed output later

Notes:
- it turns out some shellIntegration APIs are available on cursor, although not on older versions of vscode
- "By default, the shell integration script should automatically activate on supported shells launched from VS Code."
Supported shells:
Linux/macOS: bash, fish, pwsh, zsh
Windows: pwsh


Example:

const terminalManager = new TerminalManager(context);

// Run a command
const process = terminalManager.runCommand('npm install', '/path/to/project');

process.on('line', (line) => {
	console.log(line);
});

// To wait for the process to complete naturally:
await process;

// Or to continue execution even if the command is still running:
process.continue();

// Later, if you need to get the unretrieved output:
const unretrievedOutput = terminalManager.getUnretrievedOutput(terminalId);
console.log('Unretrieved output:', unretrievedOutput);

Resources:
- https://github.com/microsoft/vscode/issues/226655
- https://code.visualstudio.com/updates/v1_93#_terminal-shell-integration-api
- https://code.visualstudio.com/docs/terminal/shell-integration
- https://code.visualstudio.com/api/references/vscode-api#Terminal
- https://github.com/microsoft/vscode-extension-samples/blob/main/terminal-sample/src/extension.ts
- https://github.com/microsoft/vscode-extension-samples/blob/main/shell-integration-sample/src/extension.ts
*/

/*
The new shellIntegration API gives us access to terminal command execution output handling.
However, we don't update our VSCode type definitions or engine requirements to maintain compatibility
with older VSCode versions. Users on older versions will automatically fall back to using sendText
for terminal command execution.
Interestingly, some environments like Cursor enable these APIs even without the latest VSCode engine.
This approach allows us to leverage advanced features when available while ensuring broad compatibility.
*/
declare module "vscode" {
	// https://github.com/microsoft/vscode/blob/f0417069c62e20f3667506f4b7e53ca0004b4e3e/src/vscode-dts/vscode.d.ts#L7442
	interface Terminal {
		shellIntegration?: {
			cwd?: vscode.Uri
			executeCommand?: (command: string) => {
				read: () => AsyncIterable<string>
			}
		}
	}
	// https://github.com/microsoft/vscode/blob/f0417069c62e20f3667506f4b7e53ca0004b4e3e/src/vscode-dts/vscode.d.ts#L10794
	interface Window {
		onDidStartTerminalShellExecution?: (
			listener: (e: any) => any,
			thisArgs?: any,
			disposables?: vscode.Disposable[],
		) => vscode.Disposable
		onDidEndTerminalShellExecution?: (
			listener: (e: any) => any,
			thisArgs?: any,
			disposables?: vscode.Disposable[],
		) => vscode.Disposable
	}
}

export class TerminalManager {
	private terminalIds: Set<number> = new Set()
	private processes: Map<number, TerminalProcess> = new Map()
	private disposables: vscode.Disposable[] = []
	private shellIntegrationTimeout: number = 4000
	private globalExecutionMap: Map<any, number> = new Map() // Maps execution to terminal ID

	constructor() {
		this.setupGlobalTerminalListeners()
		this.setupTerminalStateListener()
	}

	private setupGlobalTerminalListeners() {
		// Global listener for terminal execution start
		try {
			const startDisposable = (vscode.window as vscode.Window).onDidStartTerminalShellExecution?.(async (e) => {
				if (e?.execution && e?.terminal) {
					const terminalInfo = this.findTerminalInfoByTerminal(e.terminal)
					if (terminalInfo) {
						console.log(`[TerminalManager] Global execution start detected for terminal ${terminalInfo.id}`)
						this.handleExecutionStart(terminalInfo, e.execution)
					} else {
						// This might be a terminal not managed by us, but we should still track it
						// in case it becomes relevant later
						console.log(`[TerminalManager] Execution start detected for unmanaged terminal`)
					}
				}
			})
			if (startDisposable) {
				this.disposables.push(startDisposable)
			}
		} catch (error) {
			console.error("Error setting up onDidStartTerminalShellExecution", error)
		}

		// Global listener for terminal execution end
		try {
			const endDisposable = (vscode.window as vscode.Window).onDidEndTerminalShellExecution?.(async (e) => {
				if (e?.execution && e?.terminal) {
					const terminalInfo = this.findTerminalInfoByTerminal(e.terminal)
					if (terminalInfo) {
						console.log(`[TerminalManager] Global execution end detected for terminal ${terminalInfo.id}`)
						this.handleExecutionEnd(terminalInfo, e.execution, e.exitCode)
					} else {
						console.log(`[TerminalManager] Execution end detected for unmanaged terminal`)
					}
				}
			})
			if (endDisposable) {
				this.disposables.push(endDisposable)
			}
		} catch (error) {
			console.error("Error setting up onDidEndTerminalShellExecution", error)
		}
	}

	private setupTerminalStateListener() {
		// Add a listener for terminal state changes to detect CWD updates
		try {
			const stateChangeDisposable = vscode.window.onDidChangeTerminalState((terminal) => {
				const terminalInfo = this.findTerminalInfoByTerminal(terminal)
				if (terminalInfo && terminalInfo.pendingCwdChange && terminalInfo.cwdResolved) {
					// Check if CWD has been updated to match the expected path
					if (this.isCwdMatchingExpected(terminalInfo)) {
						const resolver = terminalInfo.cwdResolved.resolve
						terminalInfo.pendingCwdChange = undefined
						terminalInfo.cwdResolved = undefined
						resolver()
					}
				}
			})
			this.disposables.push(stateChangeDisposable)
		} catch (error) {
			console.error("Error setting up onDidChangeTerminalState", error)
		}
	}

	//Find a TerminalInfo by its VSCode Terminal instance
	private findTerminalInfoByTerminal(terminal: vscode.Terminal): TerminalInfo | undefined {
		const terminals = TerminalRegistry.getAllTerminals()
		return terminals.find((t) => t.terminal === terminal)
	}

	private async handleExecutionStart(terminalInfo: TerminalInfo, execution: any) {
		try {
			// Store the current execution in the terminal buffer
			terminalInfo.buffer.currentExecution = execution
			this.globalExecutionMap.set(execution, terminalInfo.id)

			// Get command line if available
			const commandLine = execution.commandLine?.value || "unknown command"

			// Add to execution history
			terminalInfo.buffer.executionHistory.push({
				command: commandLine,
				output: "",
				startTime: Date.now()
			})

			// Start reading the execution output stream
			if (execution.read) {
				const stream = execution.read()
				this.readExecutionStream(terminalInfo, execution, stream)
			}
		} catch (error) {
			console.error("Error handling execution start:", error)
		}
	}

	private async handleExecutionEnd(terminalInfo: TerminalInfo, execution: any, exitCode?: number) {
		try {
			// Clear current execution
			if (terminalInfo.buffer.currentExecution === execution) {
				terminalInfo.buffer.currentExecution = undefined
			}
			this.globalExecutionMap.delete(execution)

			// Update execution history
			const lastExecution = terminalInfo.buffer.executionHistory[terminalInfo.buffer.executionHistory.length - 1]
			if (lastExecution && !lastExecution.endTime) {
				lastExecution.endTime = Date.now()
			}

			// Notify any waiting processes that execution has completed
			const process = this.processes.get(terminalInfo.id)
			if (process) {
				process.notifyExecutionEnd(execution, exitCode)
			}
		} catch (error) {
			console.error("Error handling execution end:", error)
		}
	}

	private async readExecutionStream(terminalInfo: TerminalInfo, _execution: any, stream: AsyncIterable<string>) {
		try {
			let isFirstChunk = true
			for await (const data of stream) {
				if (data) {
					let cleanData = data

					// Process first chunk to remove VSCode artifacts
					if (isFirstChunk) {
						cleanData = this.processFirstChunk(data)
						isFirstChunk = false
					} else {
						cleanData = stripAnsi(data)
					}

					// Add to terminal buffer
					terminalInfo.buffer.output += cleanData

					// Update execution history
					const lastExecution = terminalInfo.buffer.executionHistory[terminalInfo.buffer.executionHistory.length - 1]
					if (lastExecution && !lastExecution.endTime) {
						lastExecution.output += cleanData
					}

					// Notify any listening processes
					const process = this.processes.get(terminalInfo.id)
					if (process) {
						process.notifyOutputReceived(cleanData)
					}
				}
			}
		} catch (error) {
			console.error("Error reading execution stream:", error)
		}
	}

	private processFirstChunk(data: string): string {
		// Process VSCode shell integration sequences
		const outputBetweenSequences = this.removeLastLineArtifacts(
			data.match(/\]633;C([\s\S]*?)\]633;D/)?.[1] || "",
		).trim()

		// Remove VSCode sequences
		const vscodeSequenceRegex = /\x1b\]633;.[^\x07]*\x07/g
		const lastMatch = [...data.matchAll(vscodeSequenceRegex)].pop()
		if (lastMatch && lastMatch.index !== undefined) {
			data = data.slice(lastMatch.index + lastMatch[0].length)
		}

		// Place output back after removing vscode sequences
		if (outputBetweenSequences) {
			data = outputBetweenSequences + "\n" + data
		}

		// Remove ANSI codes
		data = stripAnsi(data)

		// Clean up terminal artifacts
		const lines = data ? data.split("\n") : []
		if (lines.length > 0) {
			lines[0] = lines[0].replace(/[^\x20-\x7E]/g, "")

			// Check for duplicated first character
			if (
				lines[0].length >= 2 &&
				lines[0][0] === lines[0][1] &&
				!["[", "{", '"', "'", "<", "("].includes(lines[0][0])
			) {
				lines[0] = lines[0].slice(1)
			}

			// Remove terminal artifacts
			lines[0] = lines[0].replace(/^[\x00-\x1F%$>#\s]*/, "")
		}
		if (lines.length > 1) {
			lines[1] = lines[1].replace(/^[\x00-\x1F%$>#\s]*/, "")
		}

		return lines.join("\n")
	}

	//Check if a terminal's CWD matches its expected pending change
	private isCwdMatchingExpected(terminalInfo: TerminalInfo): boolean {
		if (!terminalInfo.pendingCwdChange) {
			return false
		}

		const currentCwd = terminalInfo.terminal.shellIntegration?.cwd?.fsPath
		const targetCwd = vscode.Uri.file(terminalInfo.pendingCwdChange).fsPath

		if (!currentCwd) {
			return false
		}

		return arePathsEqual(currentCwd, targetCwd)
	}

	runCommand(terminalInfo: TerminalInfo, command: string): TerminalProcessResultPromise {
		terminalInfo.busy = true
		terminalInfo.lastCommand = command
		const process = new TerminalProcess()
		this.processes.set(terminalInfo.id, process)

		process.once("completed", () => {
			terminalInfo.busy = false
		})

		// if shell integration is not available, remove terminal so it does not get reused as it may be running a long-running process
		process.once("no_shell_integration", () => {
			console.log(`no_shell_integration received for terminal ${terminalInfo.id}`)
			// Remove the terminal so we can't reuse it (in case it's running a long-running process)
			TerminalRegistry.removeTerminal(terminalInfo.id)
			this.terminalIds.delete(terminalInfo.id)
			this.processes.delete(terminalInfo.id)
		})

		const promise = new Promise<void>((resolve, reject) => {
			process.once("continue", () => {
				resolve()
			})
			process.once("error", (error) => {
				console.error(`Error in terminal ${terminalInfo.id}:`, error)
				reject(error)
			})
		})

		// Ensure this terminal is tracked by our manager
		this.terminalIds.add(terminalInfo.id)

		// if shell integration is already active, run the command immediately
		if (terminalInfo.terminal.shellIntegration) {
			process.waitForShellIntegration = false
			console.log(`[TerminalManager] Running command on terminal ${terminalInfo.id}: ${command}`)
			process.run(terminalInfo.terminal, command)
		} else {
			// docs recommend waiting 3s for shell integration to activate
			console.log(
				`[TerminalManager] Waiting for shell integration for terminal ${terminalInfo.id} with timeout ${this.shellIntegrationTimeout}ms`,
			)
			pWaitFor(() => terminalInfo.terminal.shellIntegration !== undefined, {
				timeout: this.shellIntegrationTimeout,
			})
				.then(() => {
					console.log(
						`[TerminalManager] Shell integration activated for terminal ${terminalInfo.id} within timeout.`,
					)
				})
				.catch((err) => {
					console.warn(
						`[TerminalManager] Shell integration timed out or failed for terminal ${terminalInfo.id}: ${err.message}`,
					)
				})
				.finally(() => {
					console.log(`[TerminalManager] Proceeding with command execution for terminal ${terminalInfo.id}.`)
					const existingProcess = this.processes.get(terminalInfo.id)
					if (existingProcess && existingProcess.waitForShellIntegration) {
						existingProcess.waitForShellIntegration = false
						existingProcess.run(terminalInfo.terminal, command)
					}
				})
		}

		return mergePromise(process, promise)
	}

	async getOrCreateTerminal(cwd: string): Promise<TerminalInfo> {
		const terminals = TerminalRegistry.getAllTerminals()

		// Find available terminal from our pool first (created for this task)
		const matchingTerminal = terminals.find((t) => {
			if (t.busy) {
				return false
			}
			const terminalCwd = t.terminal.shellIntegration?.cwd // one of cline's commands could have changed the cwd of the terminal
			if (!terminalCwd) {
				return false
			}
			return arePathsEqual(vscode.Uri.file(cwd).fsPath, terminalCwd.fsPath)
		})
		if (matchingTerminal) {
			this.terminalIds.add(matchingTerminal.id)
			return matchingTerminal
		}

		// If no matching terminal exists, try to find any non-busy terminal
		const availableTerminal = terminals.find((t) => !t.busy)
		if (availableTerminal) {
			// Set up promise and tracking for CWD change
			const cwdPromise = new Promise<void>((resolve, reject) => {
				availableTerminal.pendingCwdChange = cwd
				availableTerminal.cwdResolved = { resolve, reject }
			})

			// Navigate back to the desired directory
			await this.runCommand(availableTerminal, `cd "${cwd}"`)

			// Either resolve immediately if CWD already updated or wait for event/timeout
			if (this.isCwdMatchingExpected(availableTerminal)) {
				if (availableTerminal.cwdResolved) {
					availableTerminal.cwdResolved.resolve()
				}
				availableTerminal.pendingCwdChange = undefined
				availableTerminal.cwdResolved = undefined
			} else {
				try {
					// Wait with a timeout for state change event to resolve
					await Promise.race([
						cwdPromise,
						new Promise<void>((_, reject) =>
							setTimeout(() => reject(new Error(`CWD timeout: Failed to update to ${cwd}`)), 1000),
						),
					])
				} catch (err) {
					// Clear pending state on timeout
					availableTerminal.pendingCwdChange = undefined
					availableTerminal.cwdResolved = undefined
				}
			}
			this.terminalIds.add(availableTerminal.id)
			return availableTerminal
		}

		// If all terminals are busy, create a new one
		const newTerminalInfo = TerminalRegistry.createTerminal(cwd)
		this.terminalIds.add(newTerminalInfo.id)
		return newTerminalInfo
	}

	getTerminals(busy: boolean): { id: number; lastCommand: string }[] {
		return Array.from(this.terminalIds)
			.map((id) => TerminalRegistry.getTerminal(id))
			.filter((t): t is TerminalInfo => t !== undefined && t.busy === busy)
			.map((t) => ({ id: t.id, lastCommand: t.lastCommand }))
	}

	getUnretrievedOutput(terminalId: number): string {
		if (!this.terminalIds.has(terminalId)) {
			return ""
		}

		const terminalInfo = TerminalRegistry.getTerminal(terminalId)
		if (!terminalInfo) {
			return ""
		}

		// Get unretrieved output from terminal buffer
		const unretrieved = terminalInfo.buffer.output.slice(terminalInfo.buffer.lastRetrievedIndex)
		terminalInfo.buffer.lastRetrievedIndex = terminalInfo.buffer.output.length

		return this.removeLastLineArtifacts(unretrieved)
	}

	// Helper method to clean up output artifacts (moved from TerminalProcess)
	private removeLastLineArtifacts(output: string): string {
		const lines = output.trimEnd().split("\n")
		if (lines.length > 0) {
			const lastLine = lines[lines.length - 1]
			// Remove prompt characters and trailing whitespace from the last line
			lines[lines.length - 1] = lastLine.replace(/[%$#>]\s*$/, "")
		}
		return lines.join("\n").trimEnd()
	}

	isProcessHot(terminalId: number): boolean {
		const process = this.processes.get(terminalId)
		return process ? process.isHot : false
	}

	disposeAll() {
		// for (const info of this.terminals) {
		// 	//info.terminal.dispose() // dont want to dispose terminals when task is aborted
		// }
		this.terminalIds.clear()
		this.processes.clear()
		this.disposables.forEach((disposable) => disposable.dispose())
		this.disposables = []
	}

	setShellIntegrationTimeout(timeout: number): void {
		this.shellIntegrationTimeout = timeout
	}
}
