---
title: "Context Management"
description: "Context is key to getting the most out of Cline"
---

> 💡 **Quick Reference**
>
> -   Context = The information Cline knows about your project
> -   Context Window = How much information <PERSON><PERSON> can hold at once
> -   Use context files to maintain project knowledge
> -   Reset when the context window gets full

## Understanding Context & Context Windows

<Frame caption="In a world of infinite context, the context window is what <PERSON><PERSON> currently has available">
	<img
		src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(2).png"
		alt="In a world of infinite context, the context window is what <PERSON><PERSON> currently has available"
	/>
</Frame>

Think of working with <PERSON><PERSON> like collaborating with a thorough, proactive teammate:

### How Context is Built

Cline actively builds context in two ways:

1. **Automatic Context Gathering (i.e. Cline-driven)**
    - Proactively reads related files
    - Explores project structure
    - Analyzes patterns and relationships
    - Maps dependencies and imports
    - Asks clarifying questions
2. **User-Guided Context**
    - Share specific files
    - Provide documentation
    - Answer C<PERSON>'s questions
    - Guide focus areas
    - Share design thoughts and requirements

💡 **Key Point**: C<PERSON> isn't passive - it actively seeks to understand your project. You can either let it explore or guide its focus, especially in [Plan](https://docs.cline.bot/exploring-clines-tools/plan-and-act-modes-a-guide-to-effective-ai-development) mode.

### Context & Context Windows

Think of context like a whiteboard you and Cline share:

-   **Context** is all the information available:
    -   What Cline has discovered
    -   What you've shared
    -   Your conversation history
    -   Project requirements
    -   Previous decisions
-   **Context Window** is the size of the whiteboard itself:
    -   Measured in tokens (1 token ≈ 3/4 of an English word)
    -   Each model has a fixed size:
        -   Claude 3.5 Sonnet: 200,000 tokens
        -   DeepSeek: 64,000 tokens
    -   When the whiteboard is full, you need to erase (clear context) to write more
        -   [How Cline manages context under the hood](https://cline.bot/blog/understanding-the-new-context-window-progress-bar-in-cline)

⚠️ **Important**: Having a large context window (like Claude's 200k tokens) doesn't mean you should fill it completely. Just like a cluttered whiteboard, too much information can make it harder to focus on what's important.

## Understanding the Context Window Progress Bar

Cline provides a visual way to monitor your context window usage through a progress bar:

<Frame caption="Visual representation of the context window usage">
	<img
		src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(1)%20(1).png"
		alt="Context window progress bar"
	/>
</Frame>

### Reading the Bar

-   ↑ shows input tokens (what you've sent to the LLM)
-   ↓ shows output tokens (what the LLM has generated)
-   The progress bar visualizes how much of your context window you've used
-   The total shows your model's maximum capacity (e.g., 200k for Claude 3.5-Sonnet)

### When to Watch the Bar

-   During long coding sessions
-   When working with multiple files
-   Before starting complex tasks
-   When Cline seems to lose context

💡 **Tip**: Consider starting a fresh session when usage reaches 70-80% to maintain optimal performance.

## Working with Context Files

Context files help maintain understanding across sessions. They serve as documentation specifically designed to help AI assistants understand your project.

#### Approaches to Context Files

1. **Evergreen Project Context (i.e.** [**Memory Bank**](https://docs.cline.bot/improving-your-prompting-skills/custom-instructions-library/cline-memory-bank)**)**
    - Living documentation that evolves with your project
    - Updated as architecture and patterns emerge
    - Example: The Memory Bank pattern maintains files like `techContext.md` and `systemPatterns.md`
    - Useful for long-running projects and teams
2. **Task-Specific Context (i.e.** [**Structured Approach**](https://cline.bot/blog/building-advanced-software-with-cline-a-structured-approach)**)**

    - Created for specific implementation tasks
    - Document requirements, constraints, and decisions
    - Example:

        ```markdown
        # auth-system-implementation.md

        ## Requirements

        -   OAuth2 implementation
        -   Support for Google and GitHub
        -   Rate limiting on auth endpoints

        ## Technical Decisions

        -   Using Passport.js for provider integration
        -   JWT for session management
        -   Redis for rate limiting
        ```

3. **Knowledge Transfer Docs**
    - Switch to plan mode and ask Cline to document everything you've accomplished so far, along with the remaining steps, in a markdown file.
    - Copy the contents of the markdown file.
    - Start a new task using that content as context.

#### Using Context Files Effectively

1. **Structure and Format**
    - Use clear, consistent organization
    - Include relevant examples
    - Link related concepts
    - Keep information focused
2. **Maintenance**
    - Update after significant changes
    - Version control your context files
    - Remove outdated information
    - Document key decisions

## Practical Tips

1. **Starting New Projects**
    - Let Cline explore the codebase
    - Answer its questions about structure and patterns
    - Consider setting up basic context files
    - Document key design decisions
2. **Ongoing Development**
    - Update context files with significant changes
    - Share relevant documentation
    - Use Plan mode for complex discussions
    - Start fresh sessions when needed
3. **Team Projects**
    - Share common context files (consider using [.clinerules](https://docs.cline.bot/improving-your-prompting-skills/prompting) files in project roots)
    - Document architectural decisions
    - Maintain consistent patterns
    - Keep documentation current

Remember: The goal is to help Cline maintain consistent understanding of your project across sessions.
