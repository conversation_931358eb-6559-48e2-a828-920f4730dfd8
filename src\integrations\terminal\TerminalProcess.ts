import { EventEmitter } from "events"

import * as vscode from "vscode"
import { Logger } from "@services/logging/Logger"

export interface TerminalProcessEvents {
	line: [line: string]
	continue: []
	completed: []
	error: [error: Error]
	no_shell_integration: []
}

// how long to wait after a process outputs anything before we consider it "cool" again
const PROCESS_HOT_TIMEOUT_NORMAL = 2_000
const PROCESS_HOT_TIMEOUT_COMPILING = 15_000

export class TerminalProcess extends EventEmitter<TerminalProcessEvents> {
	waitForShellIntegration: boolean = true
	private isListening: boolean = true
	private buffer: string = ""
	isHot: boolean = false
	private hotTimer: NodeJS.Timeout | null = null
	private currentExecution: any = null
	private executionCompleted: boolean = false
	private outputBuffer: string = ""

	constructor() {
		super()
	}

	// Method called by TerminalManager when output is received
	notifyOutputReceived(data: string) {
		if (!this.isListening) {
			return
		}

		// Process the data for hot detection
		this.processDataForHotDetection(data)

		// Add to buffer and emit lines
		this.outputBuffer += data
		this.emitIfEol(data)
	}

	// Method called by TerminalManager when execution ends
	notifyExecutionEnd(execution: any, exitCode?: number) {
		if (this.currentExecution === execution) {
			this.executionCompleted = true
			this.emitRemainingBufferIfListening()
			
			// Clear hot timer
			if (this.hotTimer) {
				clearTimeout(this.hotTimer)
			}
			this.isHot = false

			this.emit("completed")
			this.emit("continue")
		}
	}

	private processDataForHotDetection(data: string) {
		// Set to hot to stall API requests until terminal is cool again
		this.isHot = true
		if (this.hotTimer) {
			clearTimeout(this.hotTimer)
		}
		
		// these markers indicate the command is some kind of local dev server recompiling the app
		const compilingMarkers = ["compiling", "building", "bundling", "transpiling", "generating", "starting"]
		const markerNullifiers = [
			"compiled", "success", "finish", "complete", "succeed", "done", 
			"end", "stop", "exit", "terminate", "error", "fail"
		]
		const isCompiling =
			compilingMarkers.some((marker) => data.toLowerCase().includes(marker.toLowerCase())) &&
			!markerNullifiers.some((nullifier) => data.toLowerCase().includes(nullifier.toLowerCase()))
		
		this.hotTimer = setTimeout(
			() => {
				this.isHot = false
			},
			isCompiling ? PROCESS_HOT_TIMEOUT_COMPILING : PROCESS_HOT_TIMEOUT_NORMAL,
		)
	}

	async run(terminal: vscode.Terminal, command: string) {
		if (terminal.shellIntegration && terminal.shellIntegration.executeCommand) {
			// Execute the command and store the execution reference
			const execution = terminal.shellIntegration.executeCommand(command)
			this.currentExecution = execution
			this.executionCompleted = false

			// Set up timeout for first chunk (in case of broken terminal state)
			const isWindows = process.platform === "win32"
			const timeoutMs = isWindows ? 5000 : 500
			let didEmitEmptyLine = false

			const onTimeout = () => {
				Logger.debug(
					`[TerminalProcess.run] First chunk timeout hit — terminal likely in bad state. Terminating terminal.`,
				)
				try {
					terminal.dispose()
				} catch (err) {
					Logger.debug(`[TerminalProcess.run] Failed to dispose terminal: ${String(err)}`)
				}
				this.emit(
					"error",
					new Error("The command ran successfully, but we couldn't capture its output. Please proceed accordingly."),
				)
				this.emit("completed")
				this.emit("continue")
			}

			const firstChunkTimeout = setTimeout(onTimeout, timeoutMs)

			// Wait for execution to complete or timeout
			// The actual output processing is now handled by TerminalManager's global listeners
			// We just need to wait for the execution to finish
			try {
				// Check if we get any output within timeout to clear the timeout
				let hasReceivedOutput = false
				const checkOutputInterval = setInterval(() => {
					if (this.outputBuffer.length > 0 && !hasReceivedOutput) {
						hasReceivedOutput = true
						clearTimeout(firstChunkTimeout)
						if (!didEmitEmptyLine) {
							this.emit("line", "") // empty line to indicate start of command output stream
							didEmitEmptyLine = true
						}
					}
				}, 100)

				// Wait for execution to complete
				while (!this.executionCompleted) {
					await new Promise(resolve => setTimeout(resolve, 100))
				}

				clearInterval(checkOutputInterval)
				clearTimeout(firstChunkTimeout)

			} catch (error) {
				Logger.debug(`[TerminalProcess.run] Error during execution: ${String(error)}`)
				this.emit("error", error instanceof Error ? error : new Error(String(error)))
			}

		} else {
			terminal.sendText(command, true)
			// For terminals without shell integration, we can't know when the command completes
			// So we'll just emit the continue event after a delay
			this.emit("completed")
			this.emit("continue")
			this.emit("no_shell_integration")
		}
	}

	// Inspired by https://github.com/sindresorhus/execa/blob/main/lib/transform/split.js
	private emitIfEol(chunk: string) {
		this.buffer += chunk
		let lineEndIndex: number
		while ((lineEndIndex = this.buffer.indexOf("\n")) !== -1) {
			let line = this.buffer.slice(0, lineEndIndex).trimEnd() // removes trailing \r
			this.emit("line", line)
			this.buffer = this.buffer.slice(lineEndIndex + 1)
		}
	}

	private emitRemainingBufferIfListening() {
		if (this.buffer && this.isListening) {
			const remainingBuffer = this.removeLastLineArtifacts(this.buffer)
			if (remainingBuffer) {
				this.emit("line", remainingBuffer)
			}
			this.buffer = ""
		}
	}

	continue() {
		this.emitRemainingBufferIfListening()
		this.isListening = false
		this.removeAllListeners("line")
		this.emit("continue")
	}

	getUnretrievedOutput(): string {
		// This method is now deprecated as output is managed by TerminalManager
		// Return empty string to maintain compatibility
		return ""
	}

	// some processing to remove artifacts like '%' at the end of the buffer
	removeLastLineArtifacts(output: string) {
		const lines = output.trimEnd().split("\n")
		if (lines.length > 0) {
			const lastLine = lines[lines.length - 1]
			// Remove prompt characters and trailing whitespace from the last line
			lines[lines.length - 1] = lastLine.replace(/[%$#>]\s*$/, "")
		}
		return lines.join("\n").trimEnd()
	}
}

export type TerminalProcessResultPromise = TerminalProcess & Promise<void>

// Similar to execa's ResultPromise, this lets us create a mixin of both a TerminalProcess and a Promise
export function mergePromise(process: TerminalProcess, promise: Promise<void>): TerminalProcessResultPromise {
	const nativePromisePrototype = (async () => {})().constructor.prototype
	const descriptors = ["then", "catch", "finally"].map(
		(property) => [property, Reflect.getOwnPropertyDescriptor(nativePromisePrototype, property)] as const,
	)
	for (const [property, descriptor] of descriptors) {
		if (descriptor) {
			const value = descriptor.value.bind(promise)
			Reflect.defineProperty(process, property, { ...descriptor, value })
		}
	}
	return process as TerminalProcessResultPromise
}
