// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

import { Controller } from "./index"
import { StreamingResponseHandler } from "./grpc-handler"
import { handleAccountServiceRequest, handleAccountServiceStreamingRequest } from "./account/index"
import { handleBrowserServiceRequest, handleBrowserServiceStreamingRequest } from "./browser/index"
import { handleCheckpointsServiceRequest, handleCheckpointsServiceStreamingRequest } from "./checkpoints/index"
import { handleFileServiceRequest, handleFileServiceStreamingRequest } from "./file/index"
import { handleMcpServiceRequest, handleMcpServiceStreamingRequest } from "./mcp/index"
import { handleStateServiceRequest, handleStateServiceStreamingRequest } from "./state/index"
import { handleTaskServiceRequest, handleTaskServiceStreamingRequest } from "./task/index"
import { handleWebServiceRequest, handleWebServiceStreamingRequest } from "./web/index"
import { handleModelsServiceRequest, handleModelsServiceStreamingRequest } from "./models/index"
import { handleSlashServiceRequest, handleSlashServiceStreamingRequest } from "./slash/index"
import { handleUiServiceRequest, handleUiServiceStreamingRequest } from "./ui/index"

/**
 * Configuration for a service handler
 */
export interface ServiceHandlerConfig {
	requestHandler: (controller: Controller, method: string, message: any) => Promise<any>
	streamingHandler: (
		controller: Controller,
		method: string,
		message: any,
		responseStream: StreamingResponseHandler,
		requestId?: string,
	) => Promise<void>
}

/**
 * Map of service names to their handler configurations
 */
export const serviceHandlers: Record<string, ServiceHandlerConfig> = {
	"cline.AccountService": {
		requestHandler: handleAccountServiceRequest,
		streamingHandler: handleAccountServiceStreamingRequest,
	},
	"cline.BrowserService": {
		requestHandler: handleBrowserServiceRequest,
		streamingHandler: handleBrowserServiceStreamingRequest,
	},
	"cline.CheckpointsService": {
		requestHandler: handleCheckpointsServiceRequest,
		streamingHandler: handleCheckpointsServiceStreamingRequest,
	},
	"cline.FileService": {
		requestHandler: handleFileServiceRequest,
		streamingHandler: handleFileServiceStreamingRequest,
	},
	"cline.McpService": {
		requestHandler: handleMcpServiceRequest,
		streamingHandler: handleMcpServiceStreamingRequest,
	},
	"cline.StateService": {
		requestHandler: handleStateServiceRequest,
		streamingHandler: handleStateServiceStreamingRequest,
	},
	"cline.TaskService": {
		requestHandler: handleTaskServiceRequest,
		streamingHandler: handleTaskServiceStreamingRequest,
	},
	"cline.WebService": {
		requestHandler: handleWebServiceRequest,
		streamingHandler: handleWebServiceStreamingRequest,
	},
	"cline.ModelsService": {
		requestHandler: handleModelsServiceRequest,
		streamingHandler: handleModelsServiceStreamingRequest,
	},
	"cline.SlashService": {
		requestHandler: handleSlashServiceRequest,
		streamingHandler: handleSlashServiceStreamingRequest,
	},
	"cline.UiService": {
		requestHandler: handleUiServiceRequest,
		streamingHandler: handleUiServiceStreamingRequest,
	},
}
