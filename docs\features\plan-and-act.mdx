---
title: "Plan & Act"
sidebarTitle: "Plan & Act"
---

Plan & Act modes represent <PERSON><PERSON>'s approach to structured AI development, emphasizing thoughtful planning before implementation. This dual-mode system helps developers create more maintainable, accurate code while reducing iteration time.

<Frame>
	<img
		src="https://storage.googleapis.com/cline_public_images/docs/assets/planningThenActing%20(1).gif"
		alt="Use Plan to gather context before using Act to implement the plan"
	/>
</Frame>

#### Plan Mode: Think First

Plan mode is where you and <PERSON><PERSON> figure out what you're trying to build and how you'll build it. In this mode, <PERSON><PERSON>:

-   Can read your entire codebase to understand the context
-   Won't make any changes to your files
-   Focuses on understanding requirements and creating a strategy
-   Helps identify potential issues before you write a single line of code

#### Act Mode: Build It

Once you've got a plan, you switch to Act mode. Now Cline:

-   Has all the building capabilities at its disposal
-   Can make changes to your codebase
-   Still remembers everything from your planning session
-   Executes the strategy you worked out together

<Frame>
	<img src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(5).png" alt="Act mode capabilities" />
</Frame>

### Workflow Guide

When I'm working on a new feature or fixing a complex bug, here's what works for me:

1. I start in Plan mode and tell Cline what I want to build
2. Cline helps me explore the codebase, looking at relevant files
3. Together we figure out the best approach, considering edge cases and potential issues
4. When I'm confident in our plan, I switch to Act mode
5. Cline implements the solution based on our planning

#### 1. Start with Plan Mode

Begin every significant development task in Plan mode:

In this mode:

<Frame>
	<img src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(5)%20(1).png" alt="Plan mode workflow" />
</Frame>

-   Share your requirements
-   Let Cline analyze relevant files
-   Engage in dialogue to clarify objectives
-   Develop implementation strategy

<Frame>
	<img
		src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(2)%20(1)%20(1)%20(1).png"
		alt="Planning phase"
	/>
</Frame>

#### 2. Switch to Act Mode

Once you have a clear plan, switch to Act mode:

<Frame>
	<img src="https://storage.googleapis.com/cline_public_images/docs/assets/switching-to-act.gif" alt="Switching to Act mode" />
</Frame>

Act mode allows Cline to:

-   Execute against the agreed plan
-   Make changes to your codebase
-   Maintain context from planning phase

#### 3. Iterate as Needed

Complex projects often require multiple plan-act cycles:

-   Return to Plan mode when encountering unexpected complexity
-   Use Act mode for implementing solutions
-   Maintain development momentum while ensuring quality

### Best Practices

#### Planning Phase

1. Be comprehensive with requirements
2. Share relevant context upfront
3. Point Cline to relevant files if he hasn't read them
4. Validate approach before implementation

#### Implementation Phase

1. Follow the established plan
2. Monitor progress against objectives
3. Track changes and their impact
4. Document significant decisions

<Frame>
	<img
		src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(3)%20(1).png"
		alt="Implementation best practices"
	/>
</Frame>

### Power User Tips

#### Enhancing Planning

-   Use Plan mode to explore edge cases before implementation
-   Switch back to Plan when encountering unexpected complexity
-   Leverage [file reading](/features/at-mentions/file-mentions) to validate assumptions early
-   Have Cline write markdown files of the plan for future reference

### Common Patterns

#### When to Use Each Mode

I've found Plan mode works best when:

-   Starting something new where the approach isn't obvious
-   Debugging a tricky issue where I'm not sure what's wrong
-   Making architectural decisions that will affect multiple parts of the codebase
-   Trying to understand a complex workflow or feature

And Act mode is perfect for:

-   Implementing a solution we've already planned out
-   Making routine changes where the approach is clear
-   Following established patterns in the codebase
-   Running tests and making minor adjustments

<Frame>
	<img src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(6).png" alt="Mode usage patterns" />
</Frame>

### Contributing

Share your experiences and improvements:

-   Join our [Discord community](https://discord.gg/cline)
-   Participate in discussions
-   Submit feature requests
-   Report issues

---

Remember: The time invested in planning pays dividends in implementation quality and maintenance efficiency.
